// Electron renderer database service that communicates with main process via IPC
export class ElectronDatabaseService {
  private static instance: ElectronDatabaseService

  private constructor() {
    // Private constructor for singleton
  }

  public static getInstance(): ElectronDatabaseService {
    if (!ElectronDatabaseService.instance) {
      ElectronDatabaseService.instance = new ElectronDatabaseService()
    }
    return ElectronDatabaseService.instance
  }

  // Check if we're running in Electron
  private get isElectron(): boolean {
    return typeof window !== 'undefined' && window.electronAPI && window.electronAPI.isElectron
  }

  // Wait for Electron API to be ready
  private async waitForElectronAPI(maxRetries = 100): Promise<boolean> {
    for (let i = 0; i < maxRetries; i++) {
      if (this.isElectron) {
        return true
      }
      await new Promise(resolve => setTimeout(resolve, 50))
    }
    return false
  }

  // Voter CRUD operations
  public async getAllVoters() {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getAllVoters()
  }

  public async getVoterById(id: number) {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getVoterById(id)
  }

  public async getVotersByStation(stationId: number) {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getVotersByStation(stationId)
  }

  public async getVotersBySection(stationId: number, sectionId: number) {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getVotersBySection(stationId, sectionId)
  }

  // Polling station operations
  public async getAllPollingStations() {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getAllPollingStations()
  }

  public async getAllSections() {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getAllSections()
  }

  // Helper methods
  public async getOrCreatePollingStation(name: string): Promise<number> {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getOrCreatePollingStation(name)
  }

  public async getOrCreateSection(name: string): Promise<number> {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.getOrCreateSection(name)
  }

  // CSV import
  public async importCSV(csvContent: string) {
    const isReady = await this.waitForElectronAPI()
    if (!isReady) {
      throw new Error('Database operations only available in Electron environment')
    }
    return await window.electronAPI.importCSV(csvContent)
  }

  // Menu event handlers
  public onMenuImportCSV(callback: () => void): () => void {
    if (!this.isElectron) {
      return () => {} // Return empty cleanup function
    }
    return window.electronAPI.onMenuImportCSV(callback)
  }

  public onMenuExportCSV(callback: () => void): () => void {
    if (!this.isElectron) {
      return () => {} // Return empty cleanup function
    }
    return window.electronAPI.onMenuExportCSV(callback)
  }

  public onMenuExportPDF(callback: () => void): () => void {
    if (!this.isElectron) {
      return () => {} // Return empty cleanup function
    }
    return window.electronAPI.onMenuExportPDF(callback)
  }
}

export default ElectronDatabaseService

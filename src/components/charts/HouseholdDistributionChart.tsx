import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, XAxis, <PERSON>A<PERSON><PERSON>, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from "@/components/ui/chart"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Voter } from "@/types/voter"
import { calculateHouseholdDistribution, filterVotersByHouseholdSize } from "@/utils/chartUtils"

interface HouseholdDistributionChartProps {
  voters: Voter[]
  onFilterChange: (filteredVoters: Voter[]) => void
  selectedSection: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
}

const chartConfig = {
  "1-2": {
    label: "1-2 members",
    color: "var(--color-chart-1)"
  },
  "3-4": {
    label: "3-4 members",
    color: "var(--color-chart-2)"
  },
  "5-6": {
    label: "5-6 members",
    color: "var(--color-chart-3)"
  },
  "7-8": {
    label: "7-8 members",
    color: "var(--color-chart-4)"
  },
  "9+": {
    label: "9+ members",
    color: "var(--color-chart-5)"
  }
} satisfies ChartConfig

export function HouseholdDistributionChart({ voters, onFilterChange, selectedSection }: HouseholdDistributionChartProps) {
  const [selectedHousehold, setSelectedHousehold] = React.useState<string | null>(null)

  const householdData = React.useMemo(() => {
    const distribution = calculateHouseholdDistribution(voters)
    return distribution.map(item => ({
      name: item.size,
      value: item.count,
      fill: item.fill
    }))
  }, [voters])

  const handleBarClick = React.useCallback((data: any) => {
    const householdSize = data.name

    if (selectedHousehold === householdSize) {
      // Deselect - show all voters
      setSelectedHousehold(null)
      onFilterChange(voters)
    } else {
      // Select household size - filter voters
      setSelectedHousehold(householdSize)
      const filteredVoters = filterVotersByHouseholdSize(voters, householdSize)
      onFilterChange(filteredVoters)
    }
  }, [selectedHousehold, voters, onFilterChange])

  // Reset selection when section changes
  React.useEffect(() => {
    setSelectedHousehold(null)
  }, [selectedSection])

  const getTitle = () => {
    if (selectedSection) {
      return `Household Distribution - ${selectedSection.stationName}${selectedSection.sectionName ? ` (${selectedSection.sectionName})` : ''}`
    }
    return "Household Distribution - All Voters"
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">
          {getTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-2">
        <ChartContainer config={chartConfig} className="h-[220px] w-full">
          <BarChart
            data={householdData}
            layout="horizontal"
            margin={{ top: 10, right: 30, left: 60, bottom: 10 }}
          >
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              type="number"
              tick={{ fontSize: 11 }}
              className="text-muted-foreground"
            />
            <YAxis
              type="category"
              dataKey="name"
              tick={{ fontSize: 11 }}
              className="text-muted-foreground"
              width={50}
            />
            <ChartTooltip
              content={<ChartTooltipContent />}
              cursor={{ fill: 'rgba(0, 0, 0, 0.1)' }}
            />
            <Bar
              dataKey="value"
              radius={[0, 2, 2, 0]}
              onClick={handleBarClick}
              className="cursor-pointer"
              opacity={selectedHousehold ? 0.6 : 1}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

import * as React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts"
import { <PERSON><PERSON><PERSON><PERSON>, ChartTooltip, ChartTooltipContent, type ChartConfig } from "@/components/ui/chart"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Voter } from "@/types/voter"
import { calculateGenderDistribution, filterVotersByGender } from "@/utils/chartUtils"

interface GenderDistributionChartProps {
  voters: Voter[]
  onFilterChange: (filteredVoters: Voter[]) => void
  selectedSection: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
}

const chartConfig = {
  Male: {
    label: "Male",
    color: "var(--color-chart-1)"
  },
  Female: {
    label: "Female",
    color: "var(--color-chart-2)"
  },
  Other: {
    label: "Other",
    color: "var(--color-chart-3)"
  },
  Unknown: {
    label: "Unknown",
    color: "var(--color-chart-4)"
  }
} satisfies ChartConfig

export function GenderDistributionChart({ voters, onFilterChange, selectedSection }: GenderDistributionChartProps) {
  const [selectedGender, setSelectedGender] = React.useState<string | null>(null)

  const genderData = React.useMemo(() => {
    const distribution = calculateGenderDistribution(voters)
    return distribution.map(item => ({
      name: item.gender,
      value: item.count,
      fill: item.fill
    }))
  }, [voters])



  const handlePieClick = React.useCallback((data: any) => {
    const gender = data.name

    if (selectedGender === gender) {
      // Deselect - show all voters
      setSelectedGender(null)
      onFilterChange(voters)
    } else {
      // Select gender - filter voters
      setSelectedGender(gender)
      const filteredVoters = filterVotersByGender(voters, gender)
      onFilterChange(filteredVoters)
    }
  }, [selectedGender, voters, onFilterChange])



  // Reset selection when section changes
  React.useEffect(() => {
    setSelectedGender(null)
  }, [selectedSection])

  const getTitle = () => {
    if (selectedSection) {
      return `Gender Distribution - ${selectedSection.stationName}${selectedSection.sectionName ? ` (${selectedSection.sectionName})` : ''}`
    }
    return "Gender Distribution - All Voters"
  }



  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">
          {getTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-2">
        <ChartContainer config={chartConfig} className="h-[220px] w-full">
          <PieChart
            width={400}
            height={220}
            margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
          >
            <Pie
              data={genderData}
              cx="50%"
              cy="50%"
              innerRadius={40}
              outerRadius={80}
              paddingAngle={2}
              dataKey="value"
              onClick={handlePieClick}
              className="cursor-pointer"
            >
              {genderData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.fill}
                  opacity={selectedGender && selectedGender !== entry.name ? 0.5 : 1}
                />
              ))}
            </Pie>
            <ChartTooltip
              content={<ChartTooltipContent />}
            />

          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

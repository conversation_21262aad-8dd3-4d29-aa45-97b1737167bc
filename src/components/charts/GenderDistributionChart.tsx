import * as React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON> } from "recharts"
import { <PERSON><PERSON>ontainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from "@/components/ui/chart"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Voter } from "@/types/voter"
import { calculateGenderDistribution, filterVotersByGender } from "@/utils/chartUtils"

interface GenderDistributionChartProps {
  voters: Voter[]
  onFilterChange: (filteredVoters: Voter[]) => void
  selectedSection: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
}

const chartConfig = {
  Male: {
    label: "Male",
    color: "var(--color-chart-1)"
  },
  Female: {
    label: "Female",
    color: "var(--color-chart-2)"
  },
  Other: {
    label: "Other",
    color: "var(--color-chart-3)"
  },
  Unknown: {
    label: "Unknown",
    color: "var(--color-chart-4)"
  }
} satisfies ChartConfig

export function GenderDistributionChart({ voters, onFilterChange, selectedSection }: GenderDistributionChartProps) {
  const [selectedGender, setSelectedGender] = React.useState<string | null>(null)

  const genderData = React.useMemo(() => {
    const distribution = calculateGenderDistribution(voters)
    return distribution.map(item => ({
      name: item.gender,
      value: item.count,
      fill: item.fill
    }))
  }, [voters])

  const totalVoters = React.useMemo(() => {
    return genderData.reduce((sum, item) => sum + item.value, 0)
  }, [genderData])

  const handlePieClick = React.useCallback((data: any) => {
    const gender = data.name

    if (selectedGender === gender) {
      // Deselect - show all voters
      setSelectedGender(null)
      onFilterChange(voters)
    } else {
      // Select gender - filter voters
      setSelectedGender(gender)
      const filteredVoters = filterVotersByGender(voters, gender)
      onFilterChange(filteredVoters)
    }
  }, [selectedGender, voters, onFilterChange])

  const handleLegendClick = React.useCallback((entry: any) => {
    const gender = entry.value

    if (selectedGender === gender) {
      // Deselect - show all voters
      setSelectedGender(null)
      onFilterChange(voters)
    } else {
      // Select gender - filter voters
      setSelectedGender(gender)
      const filteredVoters = filterVotersByGender(voters, gender)
      onFilterChange(filteredVoters)
    }
  }, [selectedGender, voters, onFilterChange])

  // Reset selection when section changes
  React.useEffect(() => {
    setSelectedGender(null)
  }, [selectedSection])

  const getTitle = () => {
    if (selectedSection) {
      return `Gender Distribution - ${selectedSection.stationName}${selectedSection.sectionName ? ` (${selectedSection.sectionName})` : ''}`
    }
    return "Gender Distribution - All Voters"
  }

  const CustomLegend = (props: any) => {
    const { payload } = props
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-2">
        {payload.map((entry: any, index: number) => (
          <div
            key={`legend-${index}`}
            className={`flex items-center gap-2 cursor-pointer transition-opacity ${
              selectedGender && selectedGender !== entry.value ? 'opacity-50' : 'opacity-100'
            }`}
            onClick={() => handleLegendClick(entry)}
          >
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm font-medium">
              {entry.value}: {genderData.find(d => d.name === entry.value)?.value || 0}
            </span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">
          {getTitle()}
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Total: {totalVoters} voters
          {selectedGender && (
            <span className="ml-2 text-primary">
              (Filtered by {selectedGender})
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <ChartContainer config={chartConfig} className="h-[200px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <Pie
                data={genderData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
                onClick={handlePieClick}
                className="cursor-pointer"
              >
                {genderData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.fill}
                    opacity={selectedGender && selectedGender !== entry.name ? 0.5 : 1}
                  />
                ))}
              </Pie>
              <ChartTooltip
                content={<ChartTooltipContent />}
              />
              <Legend content={<CustomLegend />} />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

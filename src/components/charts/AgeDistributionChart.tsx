import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from "@/components/ui/chart"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Voter } from "@/types/voter"
import { calculateAgeDistribution, filterVotersByAge } from "@/utils/chartUtils"

interface AgeDistributionChartProps {
  voters: Voter[]
  onFilterChange: (filteredVoters: Voter[]) => void
  selectedSection: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
}

const chartConfig = {
  "18-25": {
    label: "18-25 years",
    color: "var(--color-chart-1)"
  },
  "26-35": {
    label: "26-35 years",
    color: "var(--color-chart-2)"
  },
  "36-45": {
    label: "36-45 years",
    color: "var(--color-chart-3)"
  },
  "46-60": {
    label: "46-60 years",
    color: "var(--color-chart-4)"
  },
  "60+": {
    label: "60+ years",
    color: "var(--color-chart-5)"
  }
} satisfies ChartConfig

export function AgeDistributionChart({ voters, onFilterChange, selectedSection }: AgeDistributionChartProps) {
  const [selectedAgeGroup, setSelectedAgeGroup] = React.useState<string | null>(null)

  const ageData = React.useMemo(() => {
    const distribution = calculateAgeDistribution(voters)
    return distribution.map(group => ({
      name: group.range,
      value: group.count,
      fill: group.fill
    }))
  }, [voters])

  const totalVoters = React.useMemo(() => {
    return ageData.reduce((sum, item) => sum + item.value, 0)
  }, [ageData])

  const handleBarClick = React.useCallback((data: any) => {
    const ageRange = data.name

    if (selectedAgeGroup === ageRange) {
      // Deselect - show all voters
      setSelectedAgeGroup(null)
      onFilterChange(voters)
    } else {
      // Select age group - filter voters
      setSelectedAgeGroup(ageRange)
      const filteredVoters = filterVotersByAge(voters, ageRange)
      onFilterChange(filteredVoters)
    }
  }, [selectedAgeGroup, voters, onFilterChange])



  // Reset selection when section changes
  React.useEffect(() => {
    setSelectedAgeGroup(null)
  }, [selectedSection])

  const getTitle = () => {
    if (selectedSection) {
      return `Age Distribution - ${selectedSection.stationName}${selectedSection.sectionName ? ` (${selectedSection.sectionName})` : ''}`
    }
    return "Age Distribution - All Voters"
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">
          {getTitle()}
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          {totalVoters} voters
          {selectedAgeGroup && (
            <span className="ml-2 text-primary">
              (Filtered by {selectedAgeGroup})
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <ChartContainer config={chartConfig} className="h-[200px] w-full">
          <BarChart
            data={ageData}
            width={400}
            height={200}
            margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <ChartTooltip
              content={<ChartTooltipContent />}
              cursor={{ fill: 'rgba(0, 0, 0, 0.1)' }}
            />
            <Bar
              dataKey="value"
              radius={[2, 2, 0, 0]}
              onClick={handleBarClick}
              className="cursor-pointer"
              opacity={selectedAgeGroup ? 0.6 : 1}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

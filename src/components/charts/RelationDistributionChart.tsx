import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from "@/components/ui/chart"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Voter } from "@/types/voter"
import { calculateRelationDistribution, filterVotersByRelation } from "@/utils/chartUtils"

interface RelationDistributionChartProps {
  voters: Voter[]
  onFilterChange: (filteredVoters: Voter[]) => void
  selectedSection: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
}

const chartConfig = {
  Father: {
    label: "Father",
    color: "var(--color-chart-1)"
  },
  Mother: {
    label: "Mother",
    color: "var(--color-chart-2)"
  },
  Husband: {
    label: "Husband",
    color: "var(--color-chart-3)"
  },
  Others: {
    label: "Others",
    color: "var(--color-chart-4)"
  },
  "Not Specified": {
    label: "Not Specified",
    color: "var(--color-chart-5)"
  }
} satisfies ChartConfig

export function RelationDistributionChart({ voters, onFilterChange, selectedSection }: RelationDistributionChartProps) {
  const [selectedRelation, setSelectedRelation] = React.useState<string | null>(null)

  const relationData = React.useMemo(() => {
    const distribution = calculateRelationDistribution(voters)
    return distribution.map(item => ({
      name: item.relation,
      value: item.count,
      fill: item.fill
    }))
  }, [voters])

  const totalVoters = React.useMemo(() => {
    return relationData.reduce((sum, item) => sum + item.value, 0)
  }, [relationData])

  const handleBarClick = React.useCallback((data: any) => {
    const relation = data.name

    if (selectedRelation === relation) {
      // Deselect - show all voters
      setSelectedRelation(null)
      onFilterChange(voters)
    } else {
      // Select relation - filter voters
      setSelectedRelation(relation)
      const filteredVoters = filterVotersByRelation(voters, relation)
      onFilterChange(filteredVoters)
    }
  }, [selectedRelation, voters, onFilterChange])

  const handleLegendClick = React.useCallback((dataKey: string) => {
    if (selectedRelation === dataKey) {
      // Deselect - show all voters
      setSelectedRelation(null)
      onFilterChange(voters)
    } else {
      // Select relation - filter voters
      setSelectedRelation(dataKey)
      const filteredVoters = filterVotersByRelation(voters, dataKey)
      onFilterChange(filteredVoters)
    }
  }, [selectedRelation, voters, onFilterChange])

  // Reset selection when section changes
  React.useEffect(() => {
    setSelectedRelation(null)
  }, [selectedSection])

  const getTitle = () => {
    if (selectedSection) {
      return `Relation Type - ${selectedSection.stationName}${selectedSection.sectionName ? ` (${selectedSection.sectionName})` : ''}`
    }
    return "Relation Type Distribution - All Voters"
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">
          {getTitle()}
        </CardTitle>
        <CardDescription className="text-sm text-muted-foreground">
          Total: {totalVoters} voters
          {selectedRelation && (
            <span className="ml-2 text-primary">
              (Filtered by {selectedRelation})
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <ChartContainer config={chartConfig} className="h-[200px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={relationData}
              margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
              layout="horizontal"
            >
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis
                type="number"
                tick={{ fontSize: 12 }}
                className="text-muted-foreground"
              />
              <YAxis
                type="category"
                dataKey="name"
                tick={{ fontSize: 12 }}
                className="text-muted-foreground"
                width={80}
              />
              <ChartTooltip
                content={<ChartTooltipContent />}
                cursor={{ fill: 'rgba(0, 0, 0, 0.1)' }}
              />
              <Bar
                dataKey="value"
                radius={[0, 2, 2, 0]}
                onClick={handleBarClick}
                className="cursor-pointer"
                opacity={selectedRelation ? 0.6 : 1}
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
        <div className="mt-2 flex flex-wrap justify-center gap-4">
          {relationData.map((entry, index) => (
            <div
              key={`legend-${index}`}
              className={`flex items-center gap-2 cursor-pointer transition-opacity ${
                selectedRelation && selectedRelation !== entry.name ? 'opacity-50' : 'opacity-100'
              }`}
              onClick={() => handleLegendClick(entry.name)}
            >
              <div
                className="w-3 h-3 rounded-sm"
                style={{ backgroundColor: entry.fill }}
              />
              <span className="text-sm font-medium">
                {entry.name}: {entry.value}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

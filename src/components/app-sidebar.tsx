import * as React from "react"
import { <PERSON><PERSON>ronRight, MapPin, Users } from "lucide-react"

import {
  <PERSON>bar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import ElectronDatabaseService from "@/database/electronDatabaseService"
// import { PollingStation, Section } from "@/types/voter"


// Interface for sidebar polling station data
interface SidebarPollingStation {
  id: number
  name: string
  voter_count: number
  sections: SidebarSection[]
}

interface SidebarSection {
  id: number
  name: string
  voter_count: number
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  onSectionSelect?: (section: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  }) => void
  selectedSection?: {
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null
  refreshTrigger?: number // Add a prop to trigger refresh
}

export function AppSidebar({ onSectionSelect, selectedSection, refreshTrigger, ...props }: AppSidebarProps) {
  const [pollingStations, setPollingStations] = React.useState<SidebarPollingStation[]>([])
  const [loading, setLoading] = React.useState(true)

  // Load polling stations and sections from database
  React.useEffect(() => {
    loadPollingStations()
  }, [])

  // Refresh when refreshTrigger changes
  React.useEffect(() => {
    if (refreshTrigger) {
      loadPollingStations()
    }
  }, [refreshTrigger])

  const loadPollingStations = async () => {
    try {
      setLoading(true)
      const db = ElectronDatabaseService.getInstance()

      // Get all voters to determine which sections belong to which polling stations
      const voters = await db.getAllVoters()

      // Create a map of polling station -> sections based on actual voter data
      const stationSectionMap = new Map<number, Set<number>>()
      const sectionVoterCounts = new Map<number, number>()
      const stationVoterCounts = new Map<number, number>()

      // Process voters to build relationships
      voters.forEach((voter: any) => {
        if (voter.polling_station_id && voter.section_id) {
          // Track which sections belong to which stations
          if (!stationSectionMap.has(voter.polling_station_id)) {
            stationSectionMap.set(voter.polling_station_id, new Set())
          }
          stationSectionMap.get(voter.polling_station_id)!.add(voter.section_id)

          // Count voters per section
          sectionVoterCounts.set(voter.section_id, (sectionVoterCounts.get(voter.section_id) || 0) + 1)

          // Count voters per station
          stationVoterCounts.set(voter.polling_station_id, (stationVoterCounts.get(voter.polling_station_id) || 0) + 1)
        }
      })

      // Get all polling stations and sections
      const stations = await db.getAllPollingStations()
      const sections = await db.getAllSections()

      // Build the final structure with proper relationships
      const stationsWithSections: SidebarPollingStation[] = stations.map((station: any) => {
        const stationSections = stationSectionMap.get(station.id) || new Set()

        return {
          id: station.id,
          name: station.name,
          voter_count: stationVoterCounts.get(station.id) || 0,
          sections: sections
            .filter((section: any) => stationSections.has(section.id))
            .map((section: any) => ({
              id: section.id,
              name: section.name,
              voter_count: sectionVoterCounts.get(section.id) || 0
            }))
        }
      })

      setPollingStations(stationsWithSections)
    } catch (error) {
      console.error('Failed to load polling stations:', error)
      // Set empty array on error
      setPollingStations([])
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Sidebar variant="inset" {...props}>
        <SidebarHeader>
          <SidebarMenu>
            <div className="flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm h-12">
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <span className="text-lg">8</span>
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="font-medium">MAWHATI</span>
                <span className="text-xs text-muted-foreground">Loading...</span>
              </div>
            </div>
          </SidebarMenu>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Loading Polling Stations...</SidebarGroupLabel>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    )
  }

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <div className="flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm h-12">
            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
              <span className="text-lg">8</span>
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="font-medium">MAWHATI</span>
              <span className="text-xs text-muted-foreground">Voter Management</span>
            </div>
          </div>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Polling Stations</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {pollingStations.map((station, index) => (
                <Collapsible key={station.id} className="group/collapsible">
                  <SidebarMenuItem className="flex items-center">
                    {/* Station name button: select all voters in station */}
                    <SidebarMenuButton
                      className="flex-1"
                      onClick={() =>
                        onSectionSelect?.({
                          stationId: station.id,
                          sectionId: null,
                          stationName: station.name,
                          sectionName: null,
                        })
                      }
                      aria-selected={selectedSection?.stationId === station.id && selectedSection?.sectionId == null}
                    >
                      <MapPin className="size-4" />
                      <span className="truncate flex-1 text-left capitalize">{station.name}</span>
                      <span className="text-xs text-muted-foreground">{station.voter_count}</span>
                    </SidebarMenuButton>
                    {/* Chevron only toggles expand/collapse */}
                    <CollapsibleTrigger asChild>
                      <button type="button" className="ml-2">
                        <ChevronRight className="size-4 transition-transform group-data-[state=open]/collapsible:rotate-90 group-data-[state=collapsed]/sidebar:rotate-0" />
                      </button>
                    </CollapsibleTrigger>
                  </SidebarMenuItem>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {station.sections.map((section) => {
                        const isSelected = selectedSection?.stationId === station.id && selectedSection?.sectionId === section.id
                        return (
                          <SidebarMenuSubItem key={section.id}>
                            <SidebarMenuSubButton asChild>
                              <button
                                className={`w-full flex items-center gap-2 text-left transition-colors ${
                                  isSelected
                                    ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                                    : 'hover:bg-sidebar-accent/50'
                                }`}
                                onClick={() =>
                                  onSectionSelect?.({
                                    stationId: station.id,
                                    sectionId: section.id,
                                    stationName: station.name,
                                    sectionName: section.name,
                                  })
                                }
                              >
                                <Users className="size-3" />
                                <span className="truncate text-xs">{section.name}</span>
                                <span className="ml-auto text-xs text-muted-foreground">{section.voter_count}</span>
                              </button>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        )
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}

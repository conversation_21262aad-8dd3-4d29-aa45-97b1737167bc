import * as React from "react"
import { AppSidebar } from "@/components/app-sidebar"
import { VoterTable } from "@/components/voter-table"
import { VoterDetailSheet } from "@/components/voter-detail-sheet"
import { ImportExportMenu } from "@/components/import-export-menu"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Voter, calculateAge } from "@/types/voter"
import ElectronDatabaseService from "@/database/electronDatabaseService"
import { AgeDistributionChart, GenderDistributionChart, HouseholdDistributionChart } from "@/components/charts"

function App() {
  const [selectedVoter, setSelectedVoter] = React.useState<Voter | null>(null)
  const [isDetailSheetOpen, setIsDetailSheetOpen] = React.useState(false)
  const [selectedSection, setSelectedSection] = React.useState<{
    stationId: number
    sectionId: number | null
    stationName: string
    sectionName: string | null
  } | null>(null)
  const [voters, setVoters] = React.useState<Voter[]>([])
  const [loading, setLoading] = React.useState(true)
  const [sidebarRefreshTrigger, setSidebarRefreshTrigger] = React.useState(0)
  const [chartFilteredVoters, setChartFilteredVoters] = React.useState<Voter[]>([])
  const [isChartFiltered, setIsChartFiltered] = React.useState(false)

  // Initialize database and load voters
  React.useEffect(() => {
    loadVoters()
  }, [])

  const loadVoters = async () => {
    try {
      setLoading(true)
      const db = ElectronDatabaseService.getInstance()
      const dbVoters = await db.getAllVoters()

      // Transform database voters to include calculated age
      const formattedVoters = dbVoters.map((voter: any) => ({
        ...voter,
        age: calculateAge(voter.birth_year)
      }))

      setVoters(formattedVoters)
    } catch (error) {
      console.error('Failed to load voters:', error)
      // For now, set empty array if database fails
      setVoters([])
    } finally {
      setLoading(false)
    }
  }

  // Filter voters based on selected section (show all by default, filter when section is selected)
  const sectionFilteredVoters = selectedSection
    ? selectedSection.sectionId == null
      ? voters.filter(
          (voter) => voter.polling_station_id === selectedSection.stationId
        )
      : voters.filter(
          (voter) =>
            voter.polling_station_id === selectedSection.stationId &&
            voter.section_id === selectedSection.sectionId
        )
    : voters

  // Apply chart filtering if active, otherwise use section filtered voters
  const filteredVoters = isChartFiltered ? chartFilteredVoters : sectionFilteredVoters

  const handleVoterSelect = (voter: Voter) => {
    setSelectedVoter(voter)
    setIsDetailSheetOpen(true)
  }

  const handleImportComplete = () => {
    loadVoters() // Reload voters after import
    setSidebarRefreshTrigger(prev => prev + 1) // Trigger sidebar refresh
  }

  const handleChartFilter = React.useCallback((filteredVoters: Voter[]) => {
    setChartFilteredVoters(filteredVoters)
    setIsChartFiltered(filteredVoters.length !== voters.length)
  }, [voters.length])

  const handleClearChartFilter = React.useCallback(() => {
    setChartFilteredVoters([])
    setIsChartFiltered(false)
  }, [])

  // Reset chart filter when section changes
  React.useEffect(() => {
    setChartFilteredVoters([])
    setIsChartFiltered(false)
  }, [selectedSection])

  return (
    <SidebarProvider>
      <AppSidebar
        onSectionSelect={setSelectedSection}
        selectedSection={selectedSection}
        refreshTrigger={sidebarRefreshTrigger}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="flex items-center gap-2 flex-1">
            <h1 className="font-semibold">
              {selectedSection ? selectedSection.stationName : "All Voters"}
            </h1>
            <p className="text-muted-foreground">
              {selectedSection
                ? `${selectedSection.sectionName} • ${filteredVoters.length} voters`
                : `All polling stations • ${filteredVoters.length} voters total`
              }
            </p>
          </div>
          <ImportExportMenu
            voters={filteredVoters}
            onImportComplete={handleImportComplete}
          />
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div></div>
            </div>
            <div className="grid auto-rows-min gap-4 md:grid-cols-3">
              <AgeDistributionChart
                voters={sectionFilteredVoters}
                onFilterChange={handleChartFilter}
                selectedSection={selectedSection}
              />
              <GenderDistributionChart
                voters={sectionFilteredVoters}
                onFilterChange={handleChartFilter}
                selectedSection={selectedSection}
              />
              <HouseholdDistributionChart
                voters={sectionFilteredVoters}
                onFilterChange={handleChartFilter}
                selectedSection={selectedSection}
              />
            </div>

            <VoterTable
              data={filteredVoters}
              onVoterSelect={handleVoterSelect}
              selectedSection={selectedSection}
              onClearSelection={() => setSelectedSection(null)}
              isChartFiltered={isChartFiltered}
              onClearChartFilter={handleClearChartFilter}
            />
          </div>
        </div>
      </SidebarInset>

      <VoterDetailSheet
        voter={selectedVoter}
        open={isDetailSheetOpen}
        onOpenChange={setIsDetailSheetOpen}
      />
    </SidebarProvider>
  )
}

export default App

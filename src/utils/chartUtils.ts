import { Voter } from "@/types/voter"

export interface ChartDataPoint {
  name: string
  value: number
  fill: string
}

export interface AgeGroup {
  range: string
  min: number
  max: number
  count: number
  fill: string
}

export interface GenderData {
  gender: string
  count: number
  fill: string
}

export interface HouseholdData {
  size: string
  count: number
  fill: string
}

// Age distribution utilities
export function calculateAgeDistribution(voters: Voter[]): AgeGroup[] {
  const ageGroups = [
    { range: "18-25", min: 18, max: 25, count: 0, fill: "var(--color-chart-1)" },
    { range: "26-35", min: 26, max: 35, count: 0, fill: "var(--color-chart-2)" },
    { range: "36-45", min: 36, max: 45, count: 0, fill: "var(--color-chart-3)" },
    { range: "46-60", min: 46, max: 60, count: 0, fill: "var(--color-chart-4)" },
    { range: "60+", min: 61, max: 150, count: 0, fill: "var(--color-chart-5)" }
  ]

  voters.forEach(voter => {
    const age = voter.age
    if (age >= 18) {
      const group = ageGroups.find(g => age >= g.min && age <= g.max)
      if (group) {
        group.count++
      }
    }
  })

  return ageGroups.filter(group => group.count > 0)
}

// Gender distribution utilities
export function calculateGenderDistribution(voters: Voter[]): GenderData[] {
  const genderCounts: Record<string, number> = {}

  voters.forEach(voter => {
    const gender = voter.gender || 'Unknown'
    genderCounts[gender] = (genderCounts[gender] || 0) + 1
  })

  const colors = [
    "var(--color-chart-1)",
    "var(--color-chart-2)",
    "var(--color-chart-3)"
  ]

  return Object.entries(genderCounts).map(([gender, count], index) => ({
    gender,
    count,
    fill: colors[index % colors.length]
  }))
}

// Household distribution utilities
export function calculateHouseholdDistribution(voters: Voter[]): HouseholdData[] {
  // Group voters by household (same relation_name + house_number)
  const households: Record<string, number> = {}

  voters.forEach(voter => {
    const relationName = voter.relation_name || 'Unknown'
    const houseNumber = voter.house_number || 'Unknown'
    const householdKey = `${relationName}-${houseNumber}`

    households[householdKey] = (households[householdKey] || 0) + 1
  })

  // Count households by size
  const householdSizes: Record<string, number> = {}
  Object.values(households).forEach(size => {
    let sizeCategory: string
    if (size === 1) sizeCategory = '1 member'
    else if (size === 2) sizeCategory = '2 members'
    else if (size === 3) sizeCategory = '3 members'
    else if (size === 4) sizeCategory = '4 members'
    else if (size >= 5) sizeCategory = '5+ members'
    else sizeCategory = 'Unknown'

    householdSizes[sizeCategory] = (householdSizes[sizeCategory] || 0) + 1
  })

  const colors = [
    "var(--color-chart-1)",
    "var(--color-chart-2)",
    "var(--color-chart-3)",
    "var(--color-chart-4)",
    "var(--color-chart-5)"
  ]

  return Object.entries(householdSizes).map(([size, count], index) => ({
    size,
    count,
    fill: colors[index % colors.length]
  }))
}

// Filter utilities for interactive charts
export function filterVotersByAge(voters: Voter[], ageRange: string): Voter[] {
  const ageGroups: Record<string, { min: number, max: number }> = {
    "18-25": { min: 18, max: 25 },
    "26-35": { min: 26, max: 35 },
    "36-45": { min: 36, max: 45 },
    "46-60": { min: 46, max: 60 },
    "60+": { min: 61, max: 150 }
  }

  const group = ageGroups[ageRange]
  if (!group) return voters

  return voters.filter(voter =>
    voter.age >= group.min && voter.age <= group.max
  )
}

export function filterVotersByGender(voters: Voter[], gender: string): Voter[] {
  return voters.filter(voter => voter.gender === gender)
}

export function filterVotersByHouseholdSize(voters: Voter[], sizeCategory: string): Voter[] {
  // First, group voters by household
  const households: Record<string, Voter[]> = {}

  voters.forEach(voter => {
    const relationName = voter.relation_name || 'Unknown'
    const houseNumber = voter.house_number || 'Unknown'
    const householdKey = `${relationName}-${houseNumber}`

    if (!households[householdKey]) {
      households[householdKey] = []
    }
    households[householdKey].push(voter)
  })

  // Filter households by size and return all voters from matching households
  const matchingVoters: Voter[] = []

  Object.values(households).forEach(householdVoters => {
    const size = householdVoters.length
    let matches = false

    if (sizeCategory === '1 member' && size === 1) matches = true
    else if (sizeCategory === '2 members' && size === 2) matches = true
    else if (sizeCategory === '3 members' && size === 3) matches = true
    else if (sizeCategory === '4 members' && size === 4) matches = true
    else if (sizeCategory === '5+ members' && size >= 5) matches = true

    if (matches) {
      matchingVoters.push(...householdVoters)
    }
  })

  return matchingVoters
}

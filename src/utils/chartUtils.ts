import { Voter } from "@/types/voter"

export interface ChartDataPoint {
  name: string
  value: number
  fill: string
}

export interface AgeGroup {
  range: string
  min: number
  max: number
  count: number
  fill: string
}

export interface GenderData {
  gender: string
  count: number
  fill: string
}

export interface RelationData {
  relation: string
  count: number
  fill: string
}

// Age distribution utilities
export function calculateAgeDistribution(voters: Voter[]): AgeGroup[] {
  const ageGroups = [
    { range: "18-25", min: 18, max: 25, count: 0, fill: "var(--color-chart-1)" },
    { range: "26-35", min: 26, max: 35, count: 0, fill: "var(--color-chart-2)" },
    { range: "36-45", min: 36, max: 45, count: 0, fill: "var(--color-chart-3)" },
    { range: "46-60", min: 46, max: 60, count: 0, fill: "var(--color-chart-4)" },
    { range: "60+", min: 61, max: 150, count: 0, fill: "var(--color-chart-5)" }
  ]

  voters.forEach(voter => {
    const age = voter.age
    if (age >= 18) {
      const group = ageGroups.find(g => age >= g.min && age <= g.max)
      if (group) {
        group.count++
      }
    }
  })

  return ageGroups.filter(group => group.count > 0)
}

// Gender distribution utilities
export function calculateGenderDistribution(voters: Voter[]): GenderData[] {
  const genderCounts: Record<string, number> = {}
  
  voters.forEach(voter => {
    const gender = voter.gender || 'Unknown'
    genderCounts[gender] = (genderCounts[gender] || 0) + 1
  })

  const colors = [
    "var(--color-chart-1)",
    "var(--color-chart-2)", 
    "var(--color-chart-3)"
  ]

  return Object.entries(genderCounts).map(([gender, count], index) => ({
    gender,
    count,
    fill: colors[index % colors.length]
  }))
}

// Relation type distribution utilities
export function calculateRelationDistribution(voters: Voter[]): RelationData[] {
  const relationCounts: Record<string, number> = {}
  
  voters.forEach(voter => {
    const relation = voter.relation_type || 'Not Specified'
    relationCounts[relation] = (relationCounts[relation] || 0) + 1
  })

  const colors = [
    "var(--color-chart-1)",
    "var(--color-chart-2)",
    "var(--color-chart-3)",
    "var(--color-chart-4)",
    "var(--color-chart-5)"
  ]

  return Object.entries(relationCounts).map(([relation, count], index) => ({
    relation,
    count,
    fill: colors[index % colors.length]
  }))
}

// Filter utilities for interactive charts
export function filterVotersByAge(voters: Voter[], ageRange: string): Voter[] {
  const ageGroups: Record<string, { min: number, max: number }> = {
    "18-25": { min: 18, max: 25 },
    "26-35": { min: 26, max: 35 },
    "36-45": { min: 36, max: 45 },
    "46-60": { min: 46, max: 60 },
    "60+": { min: 61, max: 150 }
  }

  const group = ageGroups[ageRange]
  if (!group) return voters

  return voters.filter(voter => 
    voter.age >= group.min && voter.age <= group.max
  )
}

export function filterVotersByGender(voters: Voter[], gender: string): Voter[] {
  return voters.filter(voter => voter.gender === gender)
}

export function filterVotersByRelation(voters: Voter[], relation: string): Voter[] {
  if (relation === 'Not Specified') {
    return voters.filter(voter => !voter.relation_type || voter.relation_type === '')
  }
  return voters.filter(voter => voter.relation_type === relation)
}

import { Voter } from "@/types/voter"

export interface ChartDataPoint {
  name: string
  value: number
  fill: string
}

export interface AgeGroup {
  range: string
  min: number
  max: number
  count: number
  fill: string
}

export interface GenderData {
  gender: string
  count: number
  fill: string
}

export interface HouseholdData {
  size: string
  count: number
  fill: string
}

// Age distribution utilities
export function calculateAgeDistribution(voters: Voter[]): AgeGroup[] {
  const ageGroups = [
    { range: "18-25", min: 18, max: 25, count: 0, fill: "var(--color-chart-1)" },
    { range: "26-35", min: 26, max: 35, count: 0, fill: "var(--color-chart-2)" },
    { range: "36-45", min: 36, max: 45, count: 0, fill: "var(--color-chart-3)" },
    { range: "46-60", min: 46, max: 60, count: 0, fill: "var(--color-chart-4)" },
    { range: "60+", min: 61, max: 150, count: 0, fill: "var(--color-chart-5)" }
  ]

  voters.forEach(voter => {
    const age = voter.age
    if (age >= 18) {
      const group = ageGroups.find(g => age >= g.min && age <= g.max)
      if (group) {
        group.count++
      }
    }
  })

  return ageGroups.filter(group => group.count > 0)
}

// Gender distribution utilities
export function calculateGenderDistribution(voters: Voter[]): GenderData[] {
  const genderCounts: Record<string, number> = {}

  voters.forEach(voter => {
    const gender = voter.gender || 'Unknown'
    genderCounts[gender] = (genderCounts[gender] || 0) + 1
  })

  const colors = [
    "var(--color-chart-1)",
    "var(--color-chart-2)",
    "var(--color-chart-3)"
  ]

  return Object.entries(genderCounts).map(([gender, count], index) => ({
    gender,
    count,
    fill: colors[index % colors.length]
  }))
}

// Household distribution utilities
export function calculateHouseholdDistribution(voters: Voter[]): HouseholdData[] {
  // If no voters, return empty array
  if (!voters || voters.length === 0) {
    return []
  }

  // Group voters by household (same relation_name + house_number)
  const households: Record<string, number> = {}

  voters.forEach(voter => {
    // Use a combination of available fields to identify households
    // If relation_name and house_number are not available, use epic_number as fallback
    const relationName = voter.relation_name || voter.name || 'Unknown'
    const houseNumber = voter.house_number || voter.epic_number || 'Unknown'
    const householdKey = `${relationName}-${houseNumber}`

    households[householdKey] = (households[householdKey] || 0) + 1
  })

  // If no households were created, create a fallback
  if (Object.keys(households).length === 0) {
    // Treat each voter as a single-person household
    voters.forEach((_, index) => {
      households[`household-${index}`] = 1
    })
  }

  // Count households by size with pair-by-pair grouping
  const householdSizes: Record<string, number> = {}
  Object.values(households).forEach(size => {
    let sizeCategory: string
    if (size >= 1 && size <= 2) sizeCategory = '1-2'
    else if (size >= 3 && size <= 4) sizeCategory = '3-4'
    else if (size >= 5 && size <= 6) sizeCategory = '5-6'
    else if (size >= 7 && size <= 8) sizeCategory = '7-8'
    else if (size >= 9) sizeCategory = '9+'
    else sizeCategory = 'Unknown'

    householdSizes[sizeCategory] = (householdSizes[sizeCategory] || 0) + 1
  })

  const colors = [
    "var(--color-chart-1)",
    "var(--color-chart-2)",
    "var(--color-chart-3)",
    "var(--color-chart-4)",
    "var(--color-chart-5)"
  ]

  const result = Object.entries(householdSizes).map(([size, count], index) => ({
    size,
    count,
    fill: colors[index % colors.length]
  }))

  // Debug log to help troubleshoot
  console.log('Household distribution calculation:', {
    voterCount: voters.length,
    householdCount: Object.keys(households).length,
    householdSizes,
    result
  })

  return result
}

// Filter utilities for interactive charts
export function filterVotersByAge(voters: Voter[], ageRange: string): Voter[] {
  const ageGroups: Record<string, { min: number, max: number }> = {
    "18-25": { min: 18, max: 25 },
    "26-35": { min: 26, max: 35 },
    "36-45": { min: 36, max: 45 },
    "46-60": { min: 46, max: 60 },
    "60+": { min: 61, max: 150 }
  }

  const group = ageGroups[ageRange]
  if (!group) return voters

  return voters.filter(voter =>
    voter.age >= group.min && voter.age <= group.max
  )
}

export function filterVotersByGender(voters: Voter[], gender: string): Voter[] {
  return voters.filter(voter => voter.gender === gender)
}

export function filterVotersByHouseholdSize(voters: Voter[], sizeCategory: string): Voter[] {
  // First, group voters by household
  const households: Record<string, Voter[]> = {}

  voters.forEach(voter => {
    const relationName = voter.relation_name || 'Unknown'
    const houseNumber = voter.house_number || 'Unknown'
    const householdKey = `${relationName}-${houseNumber}`

    if (!households[householdKey]) {
      households[householdKey] = []
    }
    households[householdKey].push(voter)
  })

  // Filter households by size and return all voters from matching households
  const matchingVoters: Voter[] = []

  Object.values(households).forEach(householdVoters => {
    const size = householdVoters.length
    let matches = false

    if (sizeCategory === '1-2' && size >= 1 && size <= 2) matches = true
    else if (sizeCategory === '3-4' && size >= 3 && size <= 4) matches = true
    else if (sizeCategory === '5-6' && size >= 5 && size <= 6) matches = true
    else if (sizeCategory === '7-8' && size >= 7 && size <= 8) matches = true
    else if (sizeCategory === '9+' && size >= 9) matches = true

    if (matches) {
      matchingVoters.push(...householdVoters)
    }
  })

  return matchingVoters
}

// Type definitions for the exposed Electron API
declare global {
  interface Window {
    electronAPI: {
      getAllVoters: () => Promise<any[]>
      getVoterById: (id: number) => Promise<any>
      getVotersByStation: (stationId: number) => Promise<any[]>
      getVotersBySection: (stationId: number, sectionId: number) => Promise<any[]>
      getAllPollingStations: () => Promise<any[]>
      getAllSections: () => Promise<any[]>
      getOrCreatePollingStation: (name: string) => Promise<number>
      getOrCreateSection: (name: string) => Promise<number>
      importCSV: (csvContent: string) => Promise<any>
      onMenuImportCSV: (callback: () => void) => () => void
      onMenuExportCSV: (callback: () => void) => () => void
      onMenuExportPDF: (callback: () => void) => () => void
      isElectron: boolean
    }
  }
}

export {}
